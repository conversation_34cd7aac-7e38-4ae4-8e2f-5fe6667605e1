/**
 * Utilitários para melhorar a experiência de desenvolvimento
 * Evita chamadas desnecessárias à API durante o desenvolvimento
 */

// Detecta se está em ambiente de desenvolvimento
export const isDevelopmentMode = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

// Detecta se o hot reload está ativo (indica que o desenvolvedor está codando)
export const isHotReloadActive = (): boolean => {
  return (
    isDevelopmentMode() && typeof module !== 'undefined' && Boolean(module.hot)
  );
};

// Verifica se deve usar dados mockados
export const shouldUseMockData = (): boolean => {
  // Usar mock se estiver em desenvolvimento E se uma flag específica estiver ativa
  return (
    isDevelopmentMode() &&
    (localStorage.getItem('CVP_USE_MOCK_DATA') === 'true' ||
      sessionStorage.getItem('CVP_USE_MOCK_DATA') === 'true')
  );
};

// Configurações de cache otimizadas para desenvolvimento
export const getDevelopmentCacheConfig = () => {
  if (isDevelopmentMode()) {
    return {
      cacheDuration: 10 * 60 * 1000, // 10 minutos em desenvolvimento
      enableCache: true,
      aggressiveCache: true, // Cache mais agressivo em desenvolvimento
    };
  }

  return {
    cacheDuration: 2 * 60 * 1000, // 2 minutos em produção
    enableCache: true,
    aggressiveCache: false,
  };
};

// Função para habilitar/desabilitar dados mockados via console
export const enableMockData = (enable: boolean = true) => {
  if (isDevelopmentMode()) {
    localStorage.setItem('CVP_USE_MOCK_DATA', enable.toString());
    console.log(
      `Mock data ${
        enable ? 'habilitado' : 'desabilitado'
      }. Recarregue a página para aplicar.`,
    );
  } else {
    console.warn(
      'Mock data só pode ser habilitado em ambiente de desenvolvimento',
    );
  }
};

// Expor função globalmente para facilitar uso no console do desenvolvedor
if (isDevelopmentMode() && typeof window !== 'undefined') {
  (window as any).CVP_enableMockData = enableMockData;
  console.log(
    '💡 Dica: Use CVP_enableMockData(true) no console para habilitar dados mockados',
  );
}
