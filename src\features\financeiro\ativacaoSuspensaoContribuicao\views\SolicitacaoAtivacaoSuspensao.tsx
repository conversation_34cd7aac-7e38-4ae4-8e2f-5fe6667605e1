import React, { useState, useEffect, useCallback, useRef } from 'react';
import * as AtivacaoSuspensao from '../exports';

export const SolicitacaoAtivacaoSuspensao: React.FC = () => {
  const {
    loading: loadingCertificado,
    response: dadosCertificado,
    invocarApiGatewayCvpComToken: buscarDadosCertificado,
  } = AtivacaoSuspensao.useRecuperarContribuicoesCertificado();

  const [showConfirmacao, setShowConfirmacao] = useState(false);
  const [contribuicaoParaSuspender, setContribuicaoParaSuspender] = useState<
    (AtivacaoSuspensao.IContribuicaoItem & { index: number }) | null
  >(null);
  const [confirmacaoFinalizada, setConfirmacaoFinalizada] = useState(false);
  const [clienteAceita, setClienteAceita] = useState(false);

  // Ref para controlar se já foi feita a primeira chamada
  const jaCarregouDados = useRef(false);

  // Memoizar a função de buscar dados para evitar recriações
  const buscarDadosMemorizado = useCallback(() => {
    if (!jaCarregouDados.current && !dadosCertificado && !loadingCertificado) {
      jaCarregouDados.current = true;
      buscarDadosCertificado();
    }
  }, [buscarDadosCertificado, dadosCertificado, loadingCertificado]);

  useEffect(() => {
    buscarDadosMemorizado();
  }, [buscarDadosMemorizado]);

  const handleToggleContribuicao =
    (
      contribuicoes: AtivacaoSuspensao.IContribuicaoItem[],
      toggleContribuicao: (index: number) => void,
    ) =>
    (index: number, novoStatus: boolean) => {
      const contribuicao = contribuicoes[index];

      if (!novoStatus && contribuicao.tipo === 'cuidadoExtra') {
        setContribuicaoParaSuspender({ ...contribuicao, index });
        setShowConfirmacao(true);
      } else {
        toggleContribuicao(index);
      }
    };

  const handleConfirmarSuspensao =
    (toggleContribuicao: (index: number) => void) => () => {
      if (contribuicaoParaSuspender && clienteAceita) {
        toggleContribuicao(contribuicaoParaSuspender.index);
        setConfirmacaoFinalizada(true);
        setShowConfirmacao(false);
      }
    };

  const handleVoltarConfirmacao = () => {
    setShowConfirmacao(false);
    setContribuicaoParaSuspender(null);
    setClienteAceita(false);
  };

  const handleGerarComprovante = () => {
    console.log('Gerar comprovante');
  };

  return (
    <AtivacaoSuspensao.AtivacaoSuspensaoContribuicaoProvider
      dadosApi={dadosCertificado}
    >
      <SolicitacaoAtivacaoSuspensaoContent
        loadingCertificado={loadingCertificado}
        dadosCertificado={dadosCertificado}
        showConfirmacao={showConfirmacao}
        contribuicaoParaSuspender={contribuicaoParaSuspender}
        confirmacaoFinalizada={confirmacaoFinalizada}
        clienteAceita={clienteAceita}
        onClienteAceitaChange={setClienteAceita}
        onConfirmarSuspensao={handleConfirmarSuspensao}
        onVoltarConfirmacao={handleVoltarConfirmacao}
        onToggleContribuicao={handleToggleContribuicao}
        onGerarComprovante={handleGerarComprovante}
      />
    </AtivacaoSuspensao.AtivacaoSuspensaoContribuicaoProvider>
  );
};

const SolicitacaoAtivacaoSuspensaoContent: React.FC<{
  loadingCertificado: boolean;
  dadosCertificado: AtivacaoSuspensao.IRecuperarContribuicoesCertificadoResponse | null;
  showConfirmacao: boolean;
  contribuicaoParaSuspender:
    | (AtivacaoSuspensao.IContribuicaoItem & { index: number })
    | null;
  confirmacaoFinalizada: boolean;
  clienteAceita: boolean;
  onClienteAceitaChange: (value: boolean) => void;
  onConfirmarSuspensao: (
    toggleContribuicao: (index: number) => void,
  ) => () => void;
  onVoltarConfirmacao: () => void;
  onToggleContribuicao: (
    contribuicoes: AtivacaoSuspensao.IContribuicaoItem[],
    toggleContribuicao: (index: number) => void,
  ) => (index: number, novoStatus: boolean) => void;
  onGerarComprovante: () => void;
}> = ({
  loadingCertificado,
  dadosCertificado,
  showConfirmacao,
  contribuicaoParaSuspender,
  confirmacaoFinalizada,
  clienteAceita,
  onClienteAceitaChange,
  onConfirmarSuspensao,
  onVoltarConfirmacao,
  onToggleContribuicao,
  onGerarComprovante,
}) => {
  const { contribuicoes, loading, toggleContribuicao } =
    AtivacaoSuspensao.useAtivacaoSuspensaoContribuicaoContext();

  return (
    <AtivacaoSuspensao.ContainerPrincipal>
      <AtivacaoSuspensao.Grid>
        <AtivacaoSuspensao.GridItem xs="1">
          <AtivacaoSuspensao.Text
            variant="heading-tiny-600"
            fontColor="content-neutral-04"
            marginBottom="24px"
          >
            {AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.SUBTITULO}
          </AtivacaoSuspensao.Text>
        </AtivacaoSuspensao.GridItem>

        <AtivacaoSuspensao.ConditionalRenderer
          condition={confirmacaoFinalizada}
        >
          <AtivacaoSuspensao.GridItem xs="1">
            <AtivacaoSuspensao.Alert
              variant="success-01"
              icon={<AtivacaoSuspensao.IconCheckRound size="medium" />}
            >
              Cuidado Extra cancelado com sucesso!
            </AtivacaoSuspensao.Alert>
          </AtivacaoSuspensao.GridItem>
        </AtivacaoSuspensao.ConditionalRenderer>

        <AtivacaoSuspensao.GridItem xs="1">
          <AtivacaoSuspensao.ConditionalRenderer
            condition={loading || loadingCertificado}
          >
            <AtivacaoSuspensao.LoadingSpinner size="large">
              <AtivacaoSuspensao.Text
                variant="text-large-400"
                fontColor="brand-primary-09"
              >
                {
                  AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.MENSAGENS
                    .CARREGANDO
                }
              </AtivacaoSuspensao.Text>
            </AtivacaoSuspensao.LoadingSpinner>
          </AtivacaoSuspensao.ConditionalRenderer>

          <AtivacaoSuspensao.ConditionalRenderer
            condition={!loading && !loadingCertificado && !showConfirmacao}
          >
            <AtivacaoSuspensao.TabelaContribuicoes
              contribuicoes={contribuicoes}
              onToggleContribuicao={onToggleContribuicao(
                contribuicoes,
                toggleContribuicao,
              )}
            />
          </AtivacaoSuspensao.ConditionalRenderer>

          {showConfirmacao && (
            <AtivacaoSuspensao.SecaoConfirmacao
              contribuicao={contribuicaoParaSuspender}
              clienteAceita={clienteAceita}
              onClienteAceitaChange={onClienteAceitaChange}
              onConfirmar={onConfirmarSuspensao(toggleContribuicao)}
              onVoltar={onVoltarConfirmacao}
              dadosCertificado={dadosCertificado}
            />
          )}
        </AtivacaoSuspensao.GridItem>

        <AtivacaoSuspensao.ConditionalRenderer
          condition={confirmacaoFinalizada}
        >
          <AtivacaoSuspensao.GridItem xs="1">
            <AtivacaoSuspensao.TabelaContribuicoes
              contribuicoes={contribuicoes}
              onToggleContribuicao={onToggleContribuicao(
                contribuicoes,
                toggleContribuicao,
              )}
            />
          </AtivacaoSuspensao.GridItem>

          <AtivacaoSuspensao.GridItem xs="1">
            <AtivacaoSuspensao.Alert
              variant="information-01"
              icon={<AtivacaoSuspensao.IconInfoRound size="medium" />}
            >
              Para continuar com o Cuidado Extra chegue na opção Incluir
              Benefício de Proteção do Menu Serviços
            </AtivacaoSuspensao.Alert>
          </AtivacaoSuspensao.GridItem>

          <AtivacaoSuspensao.GridItem xs="1">
            <AtivacaoSuspensao.Grid>
              <AtivacaoSuspensao.GridItem xs="auto">
                <AtivacaoSuspensao.Button
                  variant="primary"
                  onClick={onGerarComprovante}
                >
                  Gerar comprovante
                </AtivacaoSuspensao.Button>
              </AtivacaoSuspensao.GridItem>
            </AtivacaoSuspensao.Grid>
          </AtivacaoSuspensao.GridItem>
        </AtivacaoSuspensao.ConditionalRenderer>
      </AtivacaoSuspensao.Grid>
    </AtivacaoSuspensao.ContainerPrincipal>
  );
};
