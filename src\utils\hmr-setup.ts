/**
 * Configuração global do HMR para o projeto
 * Este arquivo deve ser importado no ponto de entrada da aplicação
 */

/**
 * <PERSON><PERSON> overlays de erro do webpack e React
 */
function clearErrorOverlays() {
  try {
    // Remove overlays de erro do webpack-dev-server
    const webpackOverlays = document.querySelectorAll(
      '#webpack-dev-server-client-overlay, [data-webpack-overlay]',
    );
    webpackOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
        console.log('🧹 Removido overlay de erro do webpack');
      }
    });

    // Remove overlays de erro do React
    const reactOverlays = document.querySelectorAll(
      'iframe[src*="webpack"], iframe[src*="react-error"]',
    );
    reactOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
        console.log('🧹 Removido overlay de erro do React');
      }
    });

    // Força re-render do React se necessário
    const reactRoots = document.querySelectorAll('[data-reactroot]');
    if (reactRoots.length > 0) {
      console.log('🔄 Forçando re-render do React após limpeza de erros');
    }

    // Limpa erros do console relacionados ao HMR
    if (typeof console.clear === 'function') {
      // Não limpa tudo, apenas logs de erro específicos
      console.log('🧹 Erros de HMR limpos');
    }
  } catch (error) {
    console.warn('⚠️ Erro ao limpar overlays:', error);
  }
}

// Configuração global para React Fast Refresh
if (process.env.NODE_ENV === 'development') {
  // Habilita logs detalhados do HMR
  if (module.hot) {
    module.hot.accept();

    // Log quando o HMR é ativado
    console.log('🔥 HMR ativado para desenvolvimento');

    // Configuração para melhor debugging
    module.hot.dispose(() => {
      console.log('🔄 HMR: Limpando módulo anterior');
    });

    // Aceita atualizações de todos os módulos filhos
    module.hot.accept(undefined, (err?: Error) => {
      if (err) {
        console.error('❌ HMR: Erro ao aceitar atualização:', err);
      } else {
        console.log('✅ HMR: Atualização aceita com sucesso');
        // Limpa erros da tela após atualização bem-sucedida
        clearErrorOverlays();
      }
    });
  }

  // Configuração específica para React Fast Refresh
  if (typeof window !== 'undefined') {
    // Força o React Fast Refresh a preservar mais estado
    const originalConsoleError = console.error;
    let errorCount = 0;

    console.error = (...args) => {
      // Filtra alguns warnings desnecessários do React Fast Refresh
      if (
        typeof args[0] === 'string' &&
        args[0].includes('Warning: React.jsx: type is invalid')
      ) {
        return;
      }

      errorCount++;
      originalConsoleError.apply(console, args);

      // Se houver muitos erros, sugere refresh
      if (errorCount > 5) {
        console.warn(
          '🔄 Muitos erros detectados. Considere recarregar a página.',
        );
        errorCount = 0; // Reset contador
      }
    };

    // Listener para detectar quando compilação é bem-sucedida
    let lastCompilationTime = Date.now();
    const checkCompilationSuccess = () => {
      const now = Date.now();
      if (now - lastCompilationTime > 1000) {
        // 1 segundo após última compilação
        clearErrorOverlays();
        errorCount = 0; // Reset contador de erros
        console.log('✅ Compilação bem-sucedida, erros limpos');
      }
      lastCompilationTime = now;
    };

    // Monitora mudanças no DOM que indicam nova compilação
    if (module.hot) {
      module.hot.accept();
      module.hot.dispose(() => {
        setTimeout(checkCompilationSuccess, 500);
      });
    }

    // Adiciona listener para mudanças de arquivo
    if (module.hot) {
      let updateTimeout: NodeJS.Timeout;

      // Type assertion para addStatusHandler que pode não estar tipado
      const hotModule = module.hot as any;
      if (hotModule.addStatusHandler) {
        hotModule.addStatusHandler((status: string) => {
          if (status === 'apply') {
            console.log('🚀 Fast Refresh: Aplicando atualizações...');

            // Debounce para evitar múltiplas atualizações
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(() => {
              console.log('✨ Fast Refresh: Componentes atualizados!');
            }, 100);
          }
        });
      }
    }
  }
}

/**
 * Função para configurar HMR em componentes específicos
 */
export function setupComponentHMR(componentName: string) {
  if (process.env.NODE_ENV === 'development' && module.hot) {
    console.log(`🔧 Configurando HMR para: ${componentName}`);

    module.hot.accept();

    // Registra o componente para debugging
    if (typeof window !== 'undefined') {
      (window as any).__HMR_COMPONENTS__ =
        (window as any).__HMR_COMPONENTS__ || [];
      (window as any).__HMR_COMPONENTS__.push(componentName);
    }
  }
}

/**
 * Função para verificar se o HMR está funcionando
 */
export function checkHMRStatus() {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Status do HMR:');
    console.log('- module.hot:', !!module.hot);
    console.log('- React Fast Refresh:', !!(window as any)?.$RefreshReg$);
    console.log(
      '- Componentes registrados:',
      (window as any)?.__HMR_COMPONENTS__ || [],
    );
  }
}

// Auto-executa a configuração quando o módulo é carregado
if (process.env.NODE_ENV === 'development') {
  setupComponentHMR('HMR-Setup');
}

export default {
  setupComponentHMR,
  checkHMRStatus,
};
