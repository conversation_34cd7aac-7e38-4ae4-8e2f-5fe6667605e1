import { Table, Button } from '@cvp/design-system-caixa';
import styled from 'styled-components';

export const AtivacaoSuspensaoTable = styled(Table)`
  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: #edf4f6;
      border: none;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;

      .rdt_TableCol_Sortable {
        font-weight: 600;
      }
    }
  }

  .rdt_TableBody {
    .rdt_TableRow:nth-child(2n) {
      background-color: #edf4f6;
    }
  }
`;

export const StatusButton = styled(Button)<{ $ativo: boolean }>`
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: ${({ $ativo }) => ($ativo ? '#28a745' : '#dc3545')};
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    opacity: 0.8;
  }
`;

export const ContainerPrincipal = styled.div`
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 1px rgba(0, 0, 0, 0.15);
`;

export const TituloSecao = styled.div`
  margin-bottom: 24px;
`;

export const ContribuicaoCard = styled.div``;

export const TotalContainer = styled.div`
  background: #f8f9fa;
  border: 2px solid #0066cc;
  border-radius: 8px;
  padding: 24px;
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
`;
